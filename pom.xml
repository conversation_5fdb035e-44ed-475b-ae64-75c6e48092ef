<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.2.2.RELEASE</version>
    </parent>

    <groupId>net.xianmu</groupId>
    <artifactId>xianmu-authentication</artifactId>
    <packaging>jar</packaging>
    <version>1.0.0</version>
    <name>authentication</name>
    <description>Demo project for Spring Boot</description>

    <properties>
        <!--    依赖版本统一配置、便于管理    -->
        <lombok.version>1.18.2</lombok.version>
        <starter.version>2.1.1</starter.version>
        <mysql-connector.version>8.0.23</mysql-connector.version>
        <mybatis.version>3.5.0</mybatis.version>
        <typehandlers.version>1.0.1</typehandlers.version>
        <datatype.version>2.9.2</datatype.version>
        <druid.version>1.2.6</druid.version>
        <fastjson.version>1.2.83</fastjson.version>
        <dingtalk.version>1.1.0</dingtalk.version>
        <redisson.version>3.11.1</redisson.version>
        <xianmu.log.version>1.0.14-RELEASE</xianmu.log.version>
        <nacos-config.version>0.2.10</nacos-config.version>
        <feishu.version>2.0.21</feishu.version>
        <guava-retry.version>2.0.0</guava-retry.version>
        <sentinel.version>1.0.1-RELEASE</sentinel.version>
    </properties>

    <dependencies>
        <!--    核心依赖模块    -->
        <dependency>
            <groupId>net.summerfarm</groupId>
            <artifactId>summerfarm-pojo</artifactId>
            <version>1.0.1</version>
        </dependency>
        <dependency>
            <groupId>net.xianmu</groupId>
            <artifactId>authentication-sdk</artifactId>
            <version>1.1.13</version>
            <exclusions>
                <exclusion>
                    <groupId>net.xianmu</groupId>
                    <artifactId>authentication-client</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>net.summerfarm</groupId>
            <artifactId>summerfarm-common</artifactId>
            <version>1.5.5-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>net.summerfarm</groupId>
            <artifactId>summerfarm-warehouse</artifactId>
            <version>1.0.4</version>
            <exclusions>
                <exclusion>
                    <artifactId>elasticsearch</artifactId>
                    <groupId>org.elasticsearch</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>elasticsearch-rest-client</artifactId>
                    <groupId>org.elasticsearch.client</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>elasticsearch-rest-high-level-client</artifactId>
                    <groupId>org.elasticsearch.client</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>net.xianmu.starter</groupId>
            <artifactId>xianmu-log-support</artifactId>
            <version>${xianmu.log.version}</version>
        </dependency>
        <!--  springboot 核心依赖包  -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-jpa</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>io.lettuce</groupId>
                    <artifactId>lettuce-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>redis.clients</groupId>
            <artifactId>jedis</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.fasterxml.jackson.core</groupId>
                    <artifactId>jackson-databind</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <!--  lombok  -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>${lombok.version}</version>
        </dependency>
        <!--mybatis整合spring组件-->
        <dependency>
            <groupId>org.mybatis.spring.boot</groupId>
            <artifactId>mybatis-spring-boot-starter</artifactId>
            <version>${starter.version}</version>
            <exclusions>
                <exclusion>
                    <groupId>org.mybatis</groupId>
                    <artifactId>mybatis</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.mybatis</groupId>
            <artifactId>mybatis</artifactId>
            <version>${mybatis.version}</version>
        </dependency>
        <!--  mybatis类型处理  -->
        <dependency>
            <groupId>org.mybatis</groupId>
            <artifactId>mybatis-typehandlers-jsr310</artifactId>
            <version>${typehandlers.version}</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <artifactId>jackson-datatype-jsr310</artifactId>
            <version>${datatype.version}</version>
        </dependency>
        <!--数据库组件——mysql连接组件-->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>${mysql-connector.version}</version>
            <scope>runtime</scope>
        </dependency>
        <!--alibaba开源数据库连接池-->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid</artifactId>
            <version>${druid.version}</version>
        </dependency>
        <!-- alibaba json -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>${fastjson.version}</version>
        </dependency>
        <!-- 钉钉 -->
        <dependency>
            <groupId>org.taobao.dingtalk</groupId>
            <artifactId>dingtalk</artifactId>
            <version>${dingtalk.version}</version>
        </dependency>
        <!-- redisson -->
        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson</artifactId>
            <version>${redisson.version}</version>
        </dependency>

        <dependency>
            <groupId>net.xianmu.starter</groupId>
            <artifactId>xianmu-dubbo-support</artifactId>
            <version>1.0.9</version>
        </dependency>
        <dependency>
            <groupId>net.xianmu.common</groupId>
            <artifactId>xianmu-common</artifactId>
            <version>1.1.14-xwk-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>net.xianmu.starter</groupId>
            <artifactId>xianmu-rocketmq-support</artifactId>
            <version>1.1.6</version>
        </dependency>

        <dependency>
            <groupId>net.xianmu</groupId>
            <artifactId>authentication-client</artifactId>
            <version>1.2.7-RELEASE</version>
        </dependency>

        <dependency>
            <groupId>com.fasterxml.jackson.dataformat</groupId>
            <artifactId>jackson-dataformat-xml</artifactId>
            <version>2.9.7</version>
        </dependency>


        <!-- hutool 工具包-->
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>5.8.11</version>
        </dependency>
        <!--        引入nacos-config版本0.2.10-->
        <dependency>
            <groupId>com.alibaba.boot</groupId>
            <artifactId>nacos-config-spring-boot-starter</artifactId>
            <version>${nacos-config.version}</version>
        </dependency>
        <dependency>
            <groupId>net.xianmu</groupId>
            <artifactId>usercenter-client</artifactId>
            <version>1.0.1</version>
        </dependency>
        <!--    定时任务    -->
        <dependency>
            <groupId>net.xianmu.starter</groupId>
            <artifactId>xianmu-task-support</artifactId>
            <version>1.0.5</version>
        </dependency>

        <dependency>
            <artifactId>okio</artifactId>
            <groupId>com.squareup.okio</groupId>
            <version>1.17.2</version>
        </dependency>
        <dependency>
            <groupId>net.xianmu.starter</groupId>
            <artifactId>xianmu-sentinel-support</artifactId>
            <version>${sentinel.version}</version>
        </dependency>

        <dependency>
            <groupId>com.github.rholder</groupId>
            <artifactId>guava-retrying</artifactId>
            <version>${guava-retry.version}</version>
        </dependency>

    </dependencies>

    <build>
        <plugins>
            <!--设置默认Java compiler-->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <showWarnings>true</showWarnings>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <mainClass>net.xianmu.authentication.AuthenticationApplication</mainClass>
                </configuration>
            </plugin>
        </plugins>
    </build>


</project>