package net.xianmu.authentication.controller;

import cn.hutool.json.JSONUtil;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.AjaxResult;
import net.xianmu.authentication.client.input.SystemOriginEnum;
import net.xianmu.authentication.client.input.purview.AuthTenantPrivilegesInput;
import net.xianmu.authentication.model.VO.AuthUserVo;
import net.xianmu.authentication.model.VO.InitVO;
import net.xianmu.authentication.model.input.AuthUserQueryInput;
import net.xianmu.authentication.model.input.UserBaseBatchUpdateInput;
import net.xianmu.authentication.service.AuthTenantPrivilegesService;
import net.xianmu.authentication.service.AuthUserService;
import net.xianmu.authentication.service.InitService;
import net.xianmu.authentication.service.impl.AuthUserServiceImpl;
import net.xianmu.common.result.CommonResult;
import net.xianmu.common.user.UserBase;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

@RestController
@RequestMapping("/auth-user")
@Slf4j
public class AuthUserController {

    @Resource
    private AuthUserService authUserService;


    /**
     * 查询各业务线用户信息
     * @param input
     * @return
     */
    @RequestMapping(value = "/queryAuthUser", method = RequestMethod.POST)
    public CommonResult<PageInfo<AuthUserVo>> queryAuthUser(@Validated @RequestBody AuthUserQueryInput input) {
        return CommonResult.ok(authUserService.selectAuthUserBySourceRoleIds(input));
    }


}
