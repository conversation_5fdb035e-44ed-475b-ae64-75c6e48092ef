package net.xianmu.authentication.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import lombok.extern.slf4j.Slf4j;
import net.summerfarm.common.exceptions.DefaultServiceException;
import net.summerfarm.common.util.MD5Util;
import net.summerfarm.common.util.PageInfoHelper;
import net.summerfarm.contexts.ResultConstant;
import net.xianmu.authentication.client.dto.*;
import net.xianmu.authentication.client.enums.AuthTypeEnum;
import net.xianmu.authentication.client.input.SystemOriginEnum;
import net.xianmu.authentication.client.input.purview.AuthTenantPrivilegesInput;
import net.xianmu.authentication.client.input.user.AuthUserAuthQueryInput;
import net.xianmu.authentication.client.input.user.AuthUserPasswordUpdateInput;
import net.xianmu.authentication.client.input.user.AuthUserUpdateInput;
import net.xianmu.authentication.client.input.user.BaseUserExtend;
import net.xianmu.authentication.client.utils.RedisKeyUtils;
import net.xianmu.authentication.common.AuthGlobal;
import net.xianmu.authentication.common.contexts.Global;
import net.xianmu.authentication.enums.UserStatusEnum;
import net.xianmu.authentication.mapper.auth.*;
import net.xianmu.authentication.model.BO.AuthUserBO;
import net.xianmu.authentication.model.VO.AuthUserVo;
import net.xianmu.authentication.model.input.AuthUserQueryInput;
import net.xianmu.authentication.service.AuthTenantPrivilegesService;
import net.xianmu.authentication.service.AuthUserBaseService;
import net.xianmu.authentication.service.AuthUserService;
import net.xianmu.common.exception.BizException;
import net.xianmu.common.result.DubboResponse;
import net.xianmu.common.user.UserBase;
import org.springframework.beans.BeanUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static net.xianmu.authentication.common.AuthGlobal.PWD_PREFIX;
import static net.xianmu.authentication.common.AuthGlobal.TENANT_ID;
import static net.xianmu.common.enums.base.auth.SystemOriginEnum.COSFO_MALL;

@Service
@Slf4j
public class AuthUserServiceImpl extends BaseService implements AuthUserService {
    @Resource
    AuthUserDao authUserDao;
    @Resource
    AuthUserBaseDao userBaseDao;
    @Resource
    AuthUserRoleDao authUserRoleDao;
    @Resource
    AuthRoleDao roleDao;
    @Resource
    AuthUserPropertiesExtDao authUserPropertiesExtDao;
    @Resource(name = "authRedisTemplate")
    RedisTemplate redisTemplate;

    @Resource(name = "redisTemplate")
    RedisTemplate baseRedisTemplate;
    @Resource
    AuthUserBaseService authUserBaseService;
    @Resource
    AuthUserAuthDao authUserAuthDao;
    @Resource
    @Lazy
    AuthLoginServiceImpl authLoginService;
    @Resource
    AuthTenantPrivilegesService authTenantPrivilegesService;
    @Override
    public UserBase createUser(SystemOriginEnum systemOriginEnum, UserBase baseUser) {
        if (systemOriginEnum == null) {
            throw new DefaultServiceException(ResultConstant.OPENID_NOT_FOUND, "系统来源不能为空");
        }
        if (systemOriginEnum.getType()>=SystemOriginEnum.COSFO_MALL.getType()){
            BaseUserExtend baseUserExtend = new BaseUserExtend();
            return createUser(systemOriginEnum,baseUser,baseUserExtend);
        }

        return createUser(systemOriginEnum, baseUser, false);
    }

    @Transactional
    public UserBase createUser(SystemOriginEnum systemOriginEnum, UserBase baseUser,Boolean oldUser) {
        if (systemOriginEnum == null) {
            throw new DefaultServiceException(ResultConstant.OPENID_NOT_FOUND, "系统来源不能为空");
        }
        String pwd = baseUser.getPassword();
        Long tenantId = baseUser.getTenantId() == null ? TENANT_ID : baseUser.getTenantId();
        //初始化用户信息
        UserBase userBase = createUserBase(systemOriginEnum, baseUser, oldUser);
        //初始化用户角色信息
        List<Long> roleIds = baseUser.getRoleIds() == null ? new ArrayList<>() : baseUser.getRoleIds();
        List<Long> roleIDs = new ArrayList<>(roleIds);
        if (roleIDs.isEmpty() && SystemOriginEnum.COSFO_OMS.getType().equals(systemOriginEnum.getType())) {
            AuthRole authRole = roleDao.selectBySourceTenantSuperRole(systemOriginEnum.getType(), tenantId);
            if (authRole == null) {
                throw new DefaultServiceException(ResultConstant.OPENID_NOT_FOUND, "初始化数据问题");
            }
            roleIDs.add(authRole.getId());
        }
        // roleIds初始化
        if (!CollectionUtils.isEmpty(baseUser.getRoleIds())) {
            List<AuthUserRole> authUserRoles = roleIDs.stream().map(it -> {
                        AuthUserRole authRle = new AuthUserRole();
                        authRle.setRoleId(it.intValue());
                        authRle.setUserId(userBase.getId().intValue());
                        authRle.setCreateTime(new Date());
                        authRle.setUpdateTime(new Date());
                        return authRle;
                    }
            ).collect(Collectors.toList());
            authUserRoleDao.batchAdd(authUserRoles);
        }
        if (SystemOriginEnum.TMS.getType().equals(systemOriginEnum.getType())
                || SystemOriginEnum.SRM.getType().equals(systemOriginEnum.getType())){
            authUserBaseService.updateAllPwd(userBase.getUserBaseId(),pwd);
        }
        return userBase;
    }

    UserBase createUserBase(SystemOriginEnum systemOriginEnum, UserBase baseUser, Boolean oldUser) {
        if (StringUtils.isEmpty(baseUser.getUsername())) {
            throw new DefaultServiceException(ResultConstant.OPENID_NOT_FOUND, "用户名不能为空");
        }
        if (StringUtils.isEmpty(baseUser.getPhone())) {
            throw new DefaultServiceException(ResultConstant.OPENID_NOT_FOUND, "手机号不能为空");
        }
        Long tenantId = baseUser.getTenantId() == null ? TENANT_ID : baseUser.getTenantId();
        // 检查手机号是否存在 一个系统和租户中
        AuthUserBase base = new AuthUserBase();
        base.setPhone(baseUser.getPhone());
        AuthUserBase userBase = userBaseDao.selectByUserBase(base);
        if (userBase == null) {
            base.setUsername(baseUser.getUsername());
            base.setPhone(null);
            userBase = userBaseDao.selectByUserBase(base);
        }
        if (userBase != null) {
            // 判断当前体系+租户是否已经存在 用户
            List<AuthUser> authUsers = authUserDao.selectByUserBaseId(userBase.getId()).stream().filter(
                    it -> it.getTenantId().equals(tenantId) && it.getSystemOrigin().equals(systemOriginEnum.getType())
            ).collect(Collectors.toList());
            if (!authUsers.isEmpty()) {
                log.info("该体系下账户已存在!authUser:{}", JSON.toJSONString(authUsers));
                throw new DefaultServiceException(ResultConstant.MNAME_EXIST, "账号已存在");
            }
            // 来自admin的特殊操作
            updateUserName(systemOriginEnum, userBase, baseUser);
            AuthUser authUser = addAuthUser(baseUser, systemOriginEnum, tenantId, userBase.getId());
            baseUser.setId(authUser.getId());
            baseUser.setUserBaseId(authUser.getUserBaseId());
            return baseUser;
        }
        //创建userBase
        AuthUserBase record = new AuthUserBase();
        BeanUtils.copyProperties(baseUser, record);
        if (!StringUtils.isEmpty(baseUser.getPassword()) && !oldUser) {
            record.setPassword(MD5Util.string2MD5(baseUser.getPassword()));
        }
        userBaseDao.insertSelective(record);
        //创建 authUser
        AuthUser authUser = addAuthUser(baseUser, systemOriginEnum, tenantId, record.getId());
        baseUser.setId(authUser.getId());
        baseUser.setUserBaseId(record.getId());

        return baseUser;
    }

    public void  updateUserName(SystemOriginEnum systemOriginEnum,AuthUserBase oldUserBase, UserBase baseUser){
        if (!systemOriginEnum.equals(SystemOriginEnum.ADMIN)){
            return;
        }
        log.info("先在别的域添加账号后 添加账号 oldUserBase:{}, new baseUser:{}", JSON.toJSONString(oldUserBase) ,JSON.toJSONString(baseUser));

        AuthUserBase update = new AuthUserBase();
        update.setId(oldUserBase.getId());
        update.setUsername(baseUser.getUsername());
        log.info("来自鲜沐-manage的操作重置了用户的username。oldUserBase:{}, new baseUser:{}", JSON.toJSONString(oldUserBase) ,JSON.toJSONString(baseUser));
        // 更新昵称
        if(oldUserBase.getNickname() == null) {
            update.setNickname(baseUser.getNickname());
        }
        userBaseDao.updateByPrimaryKeySelective(update);
    }

    private AuthUser addAuthUser(UserBase baseUser, SystemOriginEnum systemOriginEnum, Long tenantId, Long userBaseId) {
        String encryptedPassword = MD5Util.string2MD5(baseUser.getPassword());

        // 如果是COSFO_MANAGE新增用户，该用户手机号可能已经注册过其他租户。如果注册过，记录保存老密码；未注册，使用传参新密码
        if(SystemOriginEnum.COSFO_MANAGE == systemOriginEnum){
            //查询userBaseId是否注册过其他租户
            List<AuthUser> userList = authUserDao.selectByUserBaseId(userBaseId);
            //根据来源查询
            AuthUser user = Optional.ofNullable(userList).orElse(Collections.emptyList()).stream()
                    .filter(it -> Objects.equals(it.getSystemOrigin(), systemOriginEnum.getType()))
                    .findFirst()
                    .orElse(null);
            if (user != null){
                encryptedPassword = user.getPassword();
                log.info("auth新增cosfo-manage用户 tenantId={}, userBaseId={}，没有使用新密码newPassword={}, 使用了旧密码user={}", tenantId, userBaseId, baseUser.getPassword(), JSON.toJSONString(user));
            }
        }

        AuthUser authUser = new AuthUser();
        authUser.setStatus((byte) 0);
        authUser.setTenantId(tenantId);
        authUser.setUserBaseId(userBaseId);
        authUser.setUpdateTime(new Date());
        authUser.setCreateTime(new Date());
        authUser.setSystemOrigin(systemOriginEnum.getType());
        authUser.setPassword(encryptedPassword);
        authUserDao.insertSelective(authUser);
        // 来自admin和 cosfo的  user_auth bizId 也就是 id
        Long userId = authUser.getId();
        AuthUser updateAetherUser =new AuthUser();
        updateAetherUser.setId(userId);
        updateAetherUser.setBizUserId(userId);
        authUserDao.updateByPrimaryKeySelective(updateAetherUser);
        return authUser;
    }




    @Override
    @Transactional
    public UserBase updateUser(SystemOriginEnum systemOriginEnum, UserBase baseUser) {
        log.info("接收到修改用户信息请求：systemOriginEnum：{}, baseUser:{}", systemOriginEnum, JSON.toJSON(baseUser));
        Long authUserId = baseUser.getId();
        AuthUser authUser = authUserDao.selectByPrimaryKey(authUserId);
        if (authUser == null) {
            log.error("账号不存在!userId:{}", authUserId);
            throw new DefaultServiceException(ResultConstant.UNAUTHORIZED, "账号不存在");
        }
        if (baseUser.getStatus() != null) {
            authUser.setStatus(baseUser.getStatus().byteValue());
            authUser.setUpdateTime(new Date());
            authUserDao.updateByPrimaryKeySelective(authUser);
        }
        AuthUserBase userBase = userBaseDao.selectByPrimaryKey(authUser.getUserBaseId());
        if (userBase == null) {
            log.error("userBase信息不存在!authUser:{}", JSON.toJSONString(authUser));
            throw new DefaultServiceException(ResultConstant.UNAUTHORIZED, "账号绑定异常");
        }
        if (!StringUtils.isEmpty(baseUser.getPassword())) {
            updatePwd(systemOriginEnum, authUser, userBase, baseUser.getPassword());
        }
        if (!StringUtils.isEmpty(baseUser.getLogo())) {
            userBase.setLogo(baseUser.getLogo());
        }
        if (!StringUtils.isEmpty(baseUser.getEmail())) {
            userBase.setEmail(baseUser.getEmail());
        }
        if (!StringUtils.isEmpty(baseUser.getNickname()) && SystemOriginEnum.ADMIN.getType().equals(systemOriginEnum.getType())) {
            userBase.setNickname(baseUser.getNickname());
        }
        userBase.setUpdateTime(new Date());
        userBaseDao.updateByPrimaryKeySelective(userBase);
        BeanUtils.copyProperties(userBase, baseUser);
        baseUser.setPassword(null);
        baseUser.setStatus(authUser.getStatus().intValue());

        userRoles(authUserId, baseUser.getRoleIds(), baseUser.getStatus());

        log.info("用户信息更新完毕。baseUser：{}", JSON.toJSONString(baseUser));
        return baseUser;
    }

    public void updatePwd(SystemOriginEnum systemOriginEnum, AuthUser authUser, AuthUserBase authUserBase, String pwd) {
        if (systemOriginEnum.equals(SystemOriginEnum.COSFO_MANAGE) || systemOriginEnum.equals(SystemOriginEnum.COSFO_OMS)) {
            //修改来源和 base_id 的手机号
            authUserDao.updatePwdBySystemOriginEnumBaseId(systemOriginEnum.getType(),authUserBase.getId(),MD5Util.string2MD5(pwd));
            batchUpdateLastUpdatePWdTime(systemOriginEnum, authUserBase);
            return;
        }
        AuthUser updateUser = new AuthUser();
        updateUser.setId(authUser.getId());
        updateUser.setPassword(MD5Util.string2MD5(pwd));
        authUserDao.updateByPrimaryKeySelective(updateUser);
    }


    private void batchUpdateLastUpdatePWdTime(SystemOriginEnum systemOriginEnum, AuthUserBase authUserBase){
        // 根据baseid 更新
        List<AuthUser> authUsers = authUserDao.selectByUserBaseIdAndOrigin(authUserBase.getId(), systemOriginEnum.getType());

        //List<AuthUserBO> authUserBOS = authUserDao.selectUserIdPhoneBySourcePhone(systemOriginEnum.getType(), Collections.singletonList(phone));
        if (CollectionUtils.isEmpty(authUsers)){
            return;
        }
        List<Long> ids = authUsers.stream().map(AuthUser::getId).collect(Collectors.toList());
        authUserPropertiesExtDao.deleteByAllUserIdsKey(ids, AuthGlobal.LAST_UPDATE_PWD_TIME);
        List<AuthUserPropertiesExt> authUserPropertiesExts = new ArrayList<>();
        ids.forEach(
                it->{
                    AuthUserPropertiesExt authUserPropertiesExt = new AuthUserPropertiesExt();
                    authUserPropertiesExt.setPropKey(AuthGlobal.LAST_UPDATE_PWD_TIME);
                    authUserPropertiesExt.setPropValue(DateUtil.date()+"");
                    authUserPropertiesExt.setUserId(it);
                    authUserPropertiesExt.setCreateTime(new Date());
                    authUserPropertiesExt.setUpdateTime(new Date());
                    authUserPropertiesExts.add(authUserPropertiesExt);
                }
        );
        authUserPropertiesExtDao.batchAdd(authUserPropertiesExts);
        //修改密码删除锁定的redis key
        if(StrUtil.isNotBlank(authUserBase.getPhone())) {
            authLoginService.deleteBindRedis(systemOriginEnum.getType(), authUserBase.getPhone());
        }
        if(StrUtil.isNotBlank(authUserBase.getUsername())) {
            authLoginService.deleteBindRedis(systemOriginEnum.getType(), authUserBase.getUsername());
        }
    }

    private void userRoles(Long userId, List<Long> roleIds, Integer status) {
        if (CollectionUtils.isEmpty(roleIds)) {
            return;
        }
        log.info("开始更新角色信息。userId：{}, roleIds:{}", userId, JSON.toJSON(roleIds));
        List<AuthUserRole> authUserRoles = roleIds.stream().map(it -> {
                    AuthUserRole authRle = new AuthUserRole();
                    authRle.setRoleId(it.intValue());
                    authRle.setUserId(userId.intValue());
                    authRle.setCreateTime(new Date());
                    authRle.setUpdateTime(new Date());
                    return authRle;
                }
        ).collect(Collectors.toList());
        authUserRoleDao.deleteByUserIdRoleId(userId);
        authUserRoleDao.batchAdd(authUserRoles);
        synRedis(userId, roleIds, status);
    }

    private void synRedis(Long userId, List<Long> roleIds,Integer status) {
        Object o = redisTemplate.opsForValue().get(RedisKeyUtils.getAuthUserKey(userId));
        if (o == null) {
            return;
        }
        ShiroUser shiroUser = JSONUtil.toBean(o.toString(), ShiroUser.class);
        if (status != null) {
            shiroUser.setStatus(status);
        }
        List<Integer> ids = roleIds.stream().map(Long::intValue).collect(Collectors.toList());
        shiroUser.setRoleIds(ids);
        List<Integer> allRoleIds = shiroUser.getAllRoleIds();
        if (!CollectionUtils.isEmpty(allRoleIds)) {
            allRoleIds.removeAll(ids);
        }
        shiroUser.setAllRoleIds(allRoleIds);
        redisTemplate.opsForValue().set(RedisKeyUtils.getAuthUserKey(shiroUser.getId()), JSONUtil.toJsonStr(shiroUser), 4, TimeUnit.HOURS);
    }

    @Override
    public Boolean checkPhonePassword(SystemOriginEnum systemOriginEnum, String phone, String pwd) {
        authLoginService.checkSafeLogin(systemOriginEnum.getType() ,phone);
        AuthUserBase queryUserBase = new AuthUserBase();
        queryUserBase.setPhone(phone);
        AuthUserBase authUserBase = userBaseDao.selectByUserBase(queryUserBase);
        if (authUserBase == null) {
            return false;
        }
        boolean result = authUserDao.selectByUserBaseId(authUserBase.getId()).stream().anyMatch(it -> (it.getSystemOrigin().equals(systemOriginEnum.getType()) && Objects.equals(MD5Util.string2MD5(pwd), it.getPassword())));
        if (!result){
            log.error("预登录失败!phone：{}", phone);
            authLoginService.pwdErrorLoginHandler(systemOriginEnum.getType(), phone);
            return false;
        }
        authLoginService.deleteBindRedis(systemOriginEnum.getType(), phone);
        return true;
    }

    @Override
    public Boolean checkUserNamePassword(SystemOriginEnum systemOriginEnum, String userName, String pwd) {
        authLoginService.checkSafeLogin(systemOriginEnum.getType() ,userName);
        AuthUserBase authUserBase = userBaseDao.selectByNameOrigin(userName);
        if (authUserBase == null) {
            return false;
        }
        boolean result = authUserDao.selectByUserBaseId(authUserBase.getId()).stream().anyMatch(it -> (it.getSystemOrigin().equals(systemOriginEnum.getType()) && Objects.equals(MD5Util.string2MD5(pwd), it.getPassword())));
        if (!result){
            log.error("预登录失败!userName：{}", userName);
            authLoginService.pwdErrorLoginHandler(systemOriginEnum.getType(), userName);
            return false;
        }
        authLoginService.deleteBindRedis(systemOriginEnum.getType(), userName);
        return true;
    }

    @Override
    public Boolean updatePasswordByPhone(SystemOriginEnum systemOriginEnum, String phone, String password) {
//        AuthUserBase queryUserBase = new AuthUserBase();
//        queryUserBase.setPhone(phone);
//        AuthUserBase authUserBase = userBaseDao.selectByUserBase(queryUserBase);
//        if (authUserBase == null) {
//            throw new BizException("该用户不存在");
//        }
//        queryUserBase.setPhone(null);
//        queryUserBase.setId(authUserBase.getId());
//        queryUserBase.setPassword(MD5Util.string2MD5(password));
//        userBaseDao.updateByPrimaryKeySelective(queryUserBase);
        throw new BizException("废弃接口调用 updatePasswordByPhone");
    }

    @Override
    public UserBase queryUserBase(SystemOriginEnum systemOriginEnum, String userName, Long tenantId) {
        AuthUserBase authUserBase = userBaseDao.selectByNameOrigin(userName);
        if (authUserBase == null){
            return  null;
        }
        Optional<AuthUser> any = authUserDao.selectByUserBaseId(authUserBase.getId()).stream().filter(it -> Objects.equals(it.getTenantId(), tenantId) && Objects.equals(systemOriginEnum.getType(), it.getSystemOrigin())).findAny();
        UserBase userBase = new UserBase();
        mergeUserBase(userBase, authUserBase);
        if (any.isPresent()){
            AuthUser authUser = any.get();
            userBase.setId(authUser.getId());
            userBase.setTenantId(authUser.getTenantId());
            userBase.setSystemOrigin(authUser.getSystemOrigin());
        }
        return userBase;
    }

    @Override
    @Transactional
    public DubboResponse<UserBase> binlogCreateUser(SystemOriginEnum systemOriginEnum, UserBase inputUser) {
        UserBase userBase = new UserBase();
        //找到user_base
        AuthUserBase authUserBase = getAuthUserBase(inputUser);

        //合并base
        mergeUserBase(userBase, authUserBase);

        //auth信息
        Long tenantId = inputUser.getTenantId() == null ? TENANT_ID : inputUser.getTenantId();
        List<AuthUser> authUsers = authUserDao.selectByUserBaseId(userBase.getUserBaseId());
        AuthUser authUser = authUsers.stream().filter(it -> Objects.equals(it.getSystemOrigin(), systemOriginEnum.getType())
                && Objects.equals(it.getTenantId(), tenantId)).findFirst().orElse(null);
        if (authUser == null){
            //新增er
            authUser = new AuthUser();
            authUser.setUserBaseId(userBase.getUserBaseId());
            byte status = 0;
            if (userBase.getStatus()!=null){
                 status = userBase.getStatus().byteValue();
            }
            authUser.setStatus(status);
            authUser.setTenantId(tenantId);
            authUser.setSystemOrigin(systemOriginEnum.getType());
            authUser.setCreateTime(new Date());
            authUser.setUpdateTime(new Date());
            authUserDao.insertSelective(authUser);
        }else {
            if (inputUser.getStatus()!=null && authUser.getStatus().intValue()!= inputUser.getStatus()){
                AuthUser updatestatus = new AuthUser();
                updatestatus.setStatus(inputUser.getStatus().byteValue());
                updatestatus.setId(authUser.getId());
                authUserDao.updateByPrimaryKeySelective(updatestatus);
            }
        }
        userBase.setId(authUser.getId());
        userBase.setTenantId(authUser.getTenantId());
        userBase.setSystemOrigin(authUser.getSystemOrigin());
        return DubboResponse.getOK(userBase);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long selectAuthUserIdIWithInsert(AuthUser authUser) {
        if (authUser == null
                || authUser.getSystemOrigin() == null
                || authUser.getTenantId() == null
                || authUser.getUserBaseId() == null) {
            return null;
        }


        List<AuthUser> userList = authUserDao.selectByUserIdAndOrigin(authUser.getSystemOrigin(), authUser.getTenantId(), null, Collections.singletonList(authUser.getUserBaseId()));
        userList = userList.stream().filter(el -> Objects.equals(el.getStatus(), UserStatusEnum.VALID.getStatus().byteValue())).collect(Collectors.toList());

        //auth user数据，生成新数据
        if (CollectionUtils.isEmpty(userList)) {
            AuthUser insert = new AuthUser();
            insert.setSystemOrigin(authUser.getSystemOrigin());
            insert.setUserBaseId(authUser.getUserBaseId());
            insert.setTenantId(authUser.getTenantId());
            insert.setStatus(UserStatusEnum.VALID.getStatus().byteValue());
            authUserDao.insertSelective(insert);

            insert.setBizUserId(insert.getId());
            authUserDao.updateByPrimaryKeySelective(insert);

            return insert.getId();
        }

        return userList.get(0).getId();
    }

    @Override
    @Transactional
    public UserBase createUser(SystemOriginEnum systemOriginEnum, UserBase userBase, BaseUserExtend baseUserExtend) {


        if (COSFO_MALL.type > systemOriginEnum.getType()){
            return addBaseSystemOriginUser(systemOriginEnum, userBase, baseUserExtend);
        }

        //兼容 cosfo_oms 和 cosfo_manage
        check(systemOriginEnum, userBase, baseUserExtend);


        if (baseUserExtend.getDeleteAccountRelation()!=null
                &&baseUserExtend.getDeleteAccountRelation() ){
            deleteRelation(systemOriginEnum, userBase ,baseUserExtend);
            return null ;
        }
        //基础表新增
        addUserBase(userBase);

        //auth_user新增
        addAuthUser(systemOriginEnum, userBase, baseUserExtend);
        //扩展属性
        addAuthUserAuth(systemOriginEnum, userBase, baseUserExtend);

        return userBase;
    }

    @Override
    public PageInfo<AuthUserVo> selectAuthUserBySourceRoleIds(AuthUserQueryInput input) {
        int pageSize = input.getPageSize() == null ? 10 : input.getPageSize();
        int pageIndex = input.getPageIndex() == null ? 1 : input.getPageIndex();
        PageHelper.startPage(pageIndex, pageSize);
        List<AuthUserVo> userVos = userBaseDao.selectAuthUserBySourceRoleIdsAndName(input);
        return PageInfoHelper.createPageInfo(userVos);
    }

    public void deleteRelation(SystemOriginEnum systemOriginEnum, UserBase userBase, BaseUserExtend baseUserExtend){
        AuthUser authUser = authUserDao.selectByBizUserIdTenantId(systemOriginEnum.getType(), baseUserExtend.getBizUserId(),userBase.getTenantId());
        if (authUser == null){
            return;
        }
        authUserDao.deleteByPrimaryKey(authUser.getId());
        authUserAuthDao.deleteByAuthIdType(authUser.getId(), AuthTypeEnum.OFFICIAL_WE_CHAT.getType());
        authUserAuthDao.deleteByAuthIdType(authUser.getId(), AuthTypeEnum.WEI_CHAT.getType());
    }

    private void check(SystemOriginEnum systemOriginEnum, UserBase userBase, BaseUserExtend baseUserExtend) {
        if (systemOriginEnum == null || userBase == null) {
            throw new BizException("参数错误");
        }
        if (SystemOriginEnum.COSFO_MANAGE.getType() > systemOriginEnum.getType()) {
            throw new BizException("错误的系统来源");
        }
        if (StringUtils.isEmpty(userBase.getPhone())) {
            throw new BizException("手机号不能为空");
        }
        if (StringUtils.isEmpty(userBase.getTenantId())) {
            throw new BizException("租户id 不能为空");
        }
        if (StringUtils.isEmpty(userBase.getStatus())) {
            throw new BizException("用户状态不能为空");
        }
    }


    private void addUserBase(UserBase userBase){
        String phone = userBase.getPhone();
        AuthUserBase authUserBase = authLoginService.findByPhoneOrUserName(phone);
        //创建userBase
        if (authUserBase == null){
            AuthUserBase insertUserBase = new AuthUserBase();
            insertUserBase.setPhone(phone);
            insertUserBase.setUsername(phone);
            insertUserBase.setPassword(initPhonePwd(phone));
            insertUserBase.setNickname(userBase.getNickname());
            insertUserBase.setCreateTime(new Date());
            userBaseDao.insertSelective(insertUserBase);
            userBase.setUserBaseId(insertUserBase.getId());
            return ;
        }
        userBase.setUserBaseId(authUserBase.getId());
    }


    private void addAuthUser(SystemOriginEnum systemOriginEnum, UserBase userBase, BaseUserExtend baseUserExtend) {
        Long bizUserId = baseUserExtend.getBizUserId();
        AuthUser authUser = null;
        if (bizUserId != null){
            authUser = authUserDao.selectByBizUserIdTenantId(systemOriginEnum.getType(), bizUserId, userBase.getTenantId());
        }
        if (authUser == null) {
            AuthUser addUser = new AuthUser();
            addUser.setBizUserId(baseUserExtend.getBizUserId());
            addUser.setUserBaseId(userBase.getUserBaseId());
            if (userBase.getStatus() != null) {
                addUser.setStatus(userBase.getStatus().byteValue());
            }
            addUser.setSystemOrigin(systemOriginEnum.getType());
            if (StringUtils.isEmpty(userBase.getPassword())){
                addUser.setPassword(initPhonePwd(userBase.getPhone()));
            }else {
                addUser.setPassword(MD5Util.string2MD5(userBase.getPassword()));
            }
            addUser.setUpdateTime(new Date());
            addUser.setCreateTime(new Date());
            addUser.setLastLoginTime(baseUserExtend.getLastLoginTime());
            if (baseUserExtend.getAuditStatus() != null) {
                addUser.setAuditStatus(baseUserExtend.getAuditStatus().byteValue());
            }
            addUser.setTenantId(userBase.getTenantId());
            authUserDao.insertSelective(addUser);
            if (bizUserId == null){
                AuthUser updateAuthUser = new AuthUser();
                updateAuthUser.setId(addUser.getId());
                updateAuthUser.setBizUserId(addUser.getId());
                authUserDao.updateByPrimaryKeySelective(updateAuthUser);
            }
            userBase.setId(addUser.getId());
            return;
        }
        updateAuthUser(userBase,  baseUserExtend, authUser);
        userBase.setId(authUser.getId());
    }

    @Transactional
    public void updateAuthUser(SystemOriginEnum systemOriginEnum, AuthUserUpdateInput authUserUpdateInput) {
        AuthUser authUser = authUserDao.selectByBizUserId(systemOriginEnum.getType(), authUserUpdateInput.getBizId());
        if (authUser == null){
            throw new BizException("找不到用户");
        }
        AuthUserBase authUserBase = userBaseDao.selectByPrimaryKey(authUser.getUserBaseId());
        if (authUserBase == null){
            throw new BizException("找不到用户");
        }
        // 跟新删除状态 审核状态
        UserBase userBase = buildUserBase(authUserUpdateInput);
        BaseUserExtend baseUserExtend = buildBaseUserExtend(authUserUpdateInput);
        updateAuthUser(userBase, baseUserExtend, authUser);
        userBase.setId(authUser.getId());
        addAuthUserAuth(systemOriginEnum,userBase, baseUserExtend);
        // 更新手机号特殊逻辑
        updatePhone(authUser, authUserBase, authUserUpdateInput);
    }

    public void updatePhone(AuthUser authUser, AuthUserBase authUserBase, AuthUserUpdateInput authUserUpdateInput) {
        if (StringUtils.isEmpty(authUserUpdateInput.getPhone())){
            return;
        }
        if (Objects.equals(authUserBase.getPhone(), authUserUpdateInput.getPhone())) {
            return;
        }
        //修改手机号
        AuthUserBase bdBase = authLoginService.findByPhoneOrUserName(authUserUpdateInput.getPhone());
        if (bdBase == null){
            AuthUserBase updateUserBase = new AuthUserBase();
            updateUserBase.setPhone(authUserUpdateInput.getPhone());
            updateUserBase.setId(authUserBase.getId());
            if (authUserBase.getUsername().equals(authUserBase.getPhone())){
                updateUserBase.setUsername(authUserUpdateInput.getPhone());
            }
            log.info("原手机号和用户名一致，更改手机号之后，一并修改了用户名。原base信息：{}, 新的手机号：{}, 新的用户名：{}", JSON.toJSONString(authUserBase), authUserUpdateInput.getPhone(), authUserUpdateInput.getPhone());
            userBaseDao.updateByPrimaryKeySelective(updateUserBase);
        }else {
            AuthUser updateUser = new AuthUser();
            updateUser.setId(authUser.getId());
            updateUser.setUserBaseId(bdBase.getId());
            authUserDao.updateByPrimaryKeySelective(updateUser);
        }
        //删除他的绑定
        authUserAuthDao.deleteByAuthIdType(authUser.getId(), AuthTypeEnum.WEI_CHAT.getType());
        authUserAuthDao.deleteByAuthIdType(authUser.getId(), AuthTypeEnum.OFFICIAL_WE_CHAT.getType());
    }



    private UserBase buildUserBase(AuthUserUpdateInput authUserUpdateInput){
        UserBase userBase  = new UserBase();
        if (authUserUpdateInput.getStatus()!=null) {
            //更新 status
            userBase.setStatus(authUserUpdateInput.getStatus());
        }
        return userBase;
    }

    private BaseUserExtend buildBaseUserExtend(AuthUserUpdateInput authUserUpdateInput) {
        BaseUserExtend extend = new BaseUserExtend();
        if (authUserUpdateInput.getAuditStatus() != null) {
            //更新 status
            extend.setAuditStatus(authUserUpdateInput.getAuditStatus());
        }
        if(!StringUtils.isEmpty(authUserUpdateInput.getOpenId())){
            extend.setOpenid(authUserUpdateInput.getOpenId());
        }
        return extend;
    }


    public void  updateAuthUser(UserBase userBase, BaseUserExtend baseUserExtend, AuthUser authUser){
        if (userBase.getUserBaseId()!=null && !authUser.getUserBaseId().equals(userBase.getUserBaseId())) {
            //更新user_base_id
            authUserDao.updateBaseUserIdById(authUser.getId(), userBase.getUserBaseId());
        }
        if (userBase.getStatus()!=null && !Objects.equals(authUser.getStatus().intValue(), userBase.getStatus())) {
            //更新 status
            authUserDao.updateStatusById(authUser.getId(), userBase.getStatus());
        }
        if (baseUserExtend.getLastLoginTime() != null && !Objects.equals(authUser.getLastLoginTime(), baseUserExtend.getLastLoginTime())) {
            //更新最后登陆时间
            authUserDao.updateLastLoginTimeByIdTime(authUser.getId(), baseUserExtend.getLastLoginTime());
        }
        if (baseUserExtend.getAuditStatus() != null && !Objects.equals(authUser.getAuditStatus(), baseUserExtend.getAuditStatus().byteValue())) {
            //更新门店审核状态
            authUserDao.updateAuditStatusById(authUser.getId(), baseUserExtend.getAuditStatus());
        }
    }

    public void addAuthUserAuth(SystemOriginEnum systemOriginEnum, UserBase userBase, BaseUserExtend baseUserExtend){
        Long userId  = userBase.getId();

        if (systemOriginEnum.getType().equals(SystemOriginEnum.MALL.getType())){
            //商城的逻辑open_id,mp_openid可能为空
           // open_id,unionid一定是一对存在的
            String mpOpenid = baseUserExtend.getMpOpenid() == null ? "" : baseUserExtend.getMpOpenid();
            String mpUnionId = baseUserExtend.getMpUnionId() == null ? "" : baseUserExtend.getMpUnionId();
            String openId = baseUserExtend.getOpenid() == null ? "" : baseUserExtend.getOpenid();
            String unionId = baseUserExtend.getUnionId()== null ? "" : baseUserExtend.getUnionId();
            addAuth(userId, AuthTypeEnum.WEI_CHAT, mpOpenid, mpUnionId);
            addAuth(userId, AuthTypeEnum.OFFICIAL_WE_CHAT, openId, unionId);
            return;
        }
        // 小程序openID
        if (!StringUtils.isEmpty(baseUserExtend.getMpOpenid())){
            //查询是否有 open_id的
            addAuth(userId, AuthTypeEnum.WEI_CHAT, baseUserExtend.getMpOpenid(), baseUserExtend.getMpUnionId());
        }
        // 微信公众号
        if (!StringUtils.isEmpty(baseUserExtend.getOpenid())){
            addAuth(userId, AuthTypeEnum.OFFICIAL_WE_CHAT, baseUserExtend.getOpenid(), baseUserExtend.getUnionId());
        }
    }

    public void addAuth(Long userId, AuthTypeEnum authTypeEnum, String openid, String unionId) {
        //判断是否有这个条记录
        authUserAuthDao.deleteByAuthIdType(userId, authTypeEnum.getType());
        AuthUserAuth authUserAuth = new AuthUserAuth();
        authUserAuth.setAuthType(authTypeEnum.getType().byteValue());
        authUserAuth.setUserId(userId);
        authUserAuth.setThirdPartyId(unionId);
        authUserAuth.setAuthId(openid);
        authUserAuth.setCreateTime(new Date());
        authUserAuthDao.insertSelective(authUserAuth);
    }


    private AuthUserBase getAuthUserBase(UserBase baseUser) {
        String phone = baseUser.getPhone();
        //查询userBase是否存在

        AuthUserBase queryUserBase = new AuthUserBase();
        queryUserBase.setPhone(phone);
        AuthUserBase authUserBase = userBaseDao.selectByUserBase(queryUserBase);
        if (authUserBase == null) {
            return addAuthUserBase(baseUser);
        }
        if (!StringUtils.isEmpty(baseUser.getUsername()) && !Objects.equals(authUserBase.getUsername(), phone)) {
            //查询baseUser.getUsername()是否存在 若存在打出日志 ddl解决
            AuthUserBase other = userBaseDao.selectByNameOrigin(baseUser.getUsername());
            if (other == null) {
                AuthUserBase nameBase = new AuthUserBase();
                nameBase.setUsername(baseUser.getUsername());
                nameBase.setId(authUserBase.getId());
                userBaseDao.updateByPrimaryKeySelective(nameBase);
            } else {
                //查询数据是否有问题
                log.warn("追binlog数据异常 {}", JSONUtil.toJsonStr(baseUser));
            }
        }
        String nickname = baseUser.getNickname();
        if (!StringUtils.isEmpty(nickname)) {
            authUserBase.setNickname(nickname);
        }
        String password = baseUser.getPassword();
        if (!StringUtils.isEmpty(password)) {
            authUserBase.setPassword(password);
        }
        userBaseDao.updateByPrimaryKeySelective(authUserBase);
        return authUserBase;
    }



    private AuthUserBase addAuthUserBase(UserBase baseUser){
        AuthUserBase authUserBase = new AuthUserBase();
        authUserBase.setPhone(baseUser.getPhone());
        String userName = StringUtils.isEmpty(baseUser.getUsername())?baseUser.getPhone():baseUser.getUsername();
        authUserBase.setUsername(userName);
        if (userName.contains("@")){
            authUserBase.setEmail(userName);
        }
        authUserBase.setCreateTime(new Date());
        authUserBase.setUpdateTime(new Date());
        authUserBase.setPassword(baseUser.getPassword());
        userBaseDao.insert(authUserBase);
        return authUserBase;
    }

    private void mergeUserBase(UserBase userBase,AuthUserBase authUserBase){
        userBase.setUserBaseId(authUserBase.getId());
        userBase.setUsername(authUserBase.getUsername());
        userBase.setPhone(authUserBase.getPhone());
        userBase.setEmail(authUserBase.getEmail());
        userBase.setNickname(userBase.getNickname());
    }



    /**
     *  用户扩展属性
     * @param userId userId
     * @param propKey key
     * @param propValue value
     */
    public void createUserExtProperties(Long userId, String propKey, String propValue) {
        if (userId == null) {
            throw new BizException("userId不能为空");
        }
        if (StrUtil.isBlank(propKey)) {
            throw new BizException("propKey不能为空");
        }
        if (StrUtil.isBlank(propValue)) {
            throw new BizException("propValue不能为空");
        }
        UserBase userBase = getUserBase(userId);
        AuthUserPropertiesExt userPropertiesExt = new AuthUserPropertiesExt();
        userPropertiesExt.setUserId(userBase.getId());
        userPropertiesExt.setPropKey(propKey);
        userPropertiesExt.setPropValue(propValue);
        authUserPropertiesExtDao.insertSelective(userPropertiesExt);
    }

    public AuthUser getAuthUserByBaseUserIdSystem(Long baseUserId, Integer type) {
        if (baseUserId == null || type == null) {
            return null;
        }
        return authUserDao.selectByUserBaseId(baseUserId).stream()
                .filter(it->it.getSystemOrigin().equals(type)).findFirst().orElse(null);
    }

    private String initPhonePwd(String phone){
        return PWD_PREFIX+ MD5Util.string2MD5(phone);
    }


    public UserBase addBaseSystemOriginUser(SystemOriginEnum systemOriginEnum, UserBase userBase, BaseUserExtend baseUserExtend) {
        log.warn("老的创建cosfo-manage、cosfo-oms账号的入口被触发了");
        UserBase user = createUser(systemOriginEnum, userBase, false);
        if (user != null && baseUserExtend != null) {
            if (!CollectionUtils.isEmpty(baseUserExtend.getAuthEquityInputs()) && !Objects.equals(userBase.getTenantId(), TENANT_ID)) {
                AuthTenantPrivilegesInput authTenantPrivilegesInput = new AuthTenantPrivilegesInput();
                authTenantPrivilegesInput.setTenantPrivilegesInputs(baseUserExtend.getAuthEquityInputs());
                authTenantPrivilegesInput.setSystemOrigin(systemOriginEnum.getType());
                authTenantPrivilegesInput.setTenantId(userBase.getTenantId());
                authTenantPrivilegesService.addTenantPrivileges(authTenantPrivilegesInput);
            }
        }
        return user;
    }



    /**
     * 根据手机号修改指定来源和租户的密码
     */
    public void updateAuthUserPassword(AuthUserPasswordUpdateInput dto) {
        log.info("auth-server  接收到修改用户密码请求.dto :{}", JSON.toJSONString(dto));
        AuthUser authUser = authUserDao.selectByBizUserId(dto.getOrigin(), dto.getBizUserId());
        if(authUser == null) {
            log.error("用户信息不存在!");
            throw new BizException("用户信息不存在!");
        }
        String encryptedPassword = org.apache.commons.lang3.StringUtils.isBlank(dto.getPassword()) ? PWD_PREFIX+ MD5Util.string2MD5("" + dto.getBizUserId()):MD5Util.string2MD5(dto.getPassword());
        Integer origin = dto.getOrigin();
        List<Long> updatePasswordAuthUserIdList = new ArrayList<>();
        List<AuthUser> userList = authUserDao.selectByUserBaseId(authUser.getUserBaseId());
        AuthUser user= null;

        // 查询需要修改密码的列表
        if(SystemOriginEnum.COSFO_MANAGE.getType().equals(origin)){
            updatePasswordAuthUserIdList.addAll(userList.stream()
                    .filter(it -> Objects.equals(it.getSystemOrigin(), origin))
                    .map(AuthUser::getId).collect(Collectors.toList()));

            if(dto.isKeepOldPassword()) {
                //根据来源查询
                user = userList.stream()
                        .filter(it -> Objects.equals(it.getSystemOrigin(), origin))
                        .filter(it -> !Objects.equals(it.getId(), authUser.getId()))
                        .findFirst()
                        .orElse(null);
                if (user != null){
                    encryptedPassword = user.getPassword();
                    log.info("auth-sdk修改密码 沿用了旧密码.dto:{}", JSON.toJSONString(dto));
                }
            }
        } else if(SystemOriginEnum.COSFO_MALL.getType().equals(origin)){
            //根据来源 + 租户 查询
            updatePasswordAuthUserIdList.addAll(userList.stream()
                    .filter(it -> Objects.equals(it.getSystemOrigin(), origin))
                    .filter(it -> Objects.equals(it.getTenantId(), authUser.getTenantId()))
                    .map(AuthUser::getId)
                    .collect(Collectors.toList()));
            if(dto.isKeepOldPassword()) {
                user = userList.stream()
                        .filter(it -> Objects.equals(it.getSystemOrigin(), origin))
                        .filter(it -> !Objects.equals(it.getId(), authUser.getId()))
                        .filter(it -> Objects.equals(it.getTenantId(), authUser.getTenantId()))
                        .findFirst()
                        .orElse(null);

                if (user != null){
                    encryptedPassword = user.getPassword();
                    log.info("auth-sdk修改密码 沿用了旧密码.dto:{}", JSON.toJSONString(dto));
                }
            }

        } else {
            updatePasswordAuthUserIdList.add(authUser.getId());
        }
        log.info("开始更新用户密码：updatePasswordAuthUserIdList：{}, encryptedPassword:{}", JSON.toJSONString(updatePasswordAuthUserIdList), encryptedPassword);
        authUserDao.updatePwdByIds(updatePasswordAuthUserIdList, encryptedPassword);
    }
}
