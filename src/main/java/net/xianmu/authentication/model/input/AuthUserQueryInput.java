package net.xianmu.authentication.model.input;

import lombok.Data;
import lombok.EqualsAndHashCode;
import net.xianmu.common.input.BasePageInput;
import net.xianmu.common.user.UserBase;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 * @descripton
 * @date 2025/3/12 14:01
 */

@EqualsAndHashCode(callSuper = true)
@Data
public class AuthUserQueryInput extends BasePageInput {

    private String userName;

    private String nickname;

    private Integer status;

    /**
     * 不包含的角色列表
     */
    private List<Long> notExcludeRoleIds;

    /**
     * 包含的角色列表
     */
    private List<Long> roleIds;

    @NotNull(message = "系统来源不能为空")
    private Integer systemOrigin;
}
